/* Estilos para o Modal de Login */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
  backdrop-filter: blur(5px);
  /* Posicionamento específico do topo */
  padding-top: 120px;
}

.modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  padding: 30px;
  width: 100%;
  max-width: 450px;
  max-height: calc(100vh - 140px);
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  flex-shrink: 0;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  color: #ffffff;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(45deg, #4ade80, #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.closeButton {
  background: none;
  border: none;
  color: #888;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
}

.closeButton:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

/* Descrição */
.description {
  margin-bottom: 25px;
}

.description p {
  color: #cccccc;
  font-size: 14px;
  margin: 0;
  text-align: center;
  line-height: 1.5;
}

/* Formulário */
.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.label svg {
  color: #4ade80;
}

.input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 14px 16px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.2s;
  width: 100%;
  box-sizing: border-box;
}

.input:focus {
  outline: none;
  border-color: #4ade80;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
}

.input::placeholder {
  color: #888;
}

.input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Container de senha com botão de mostrar/ocultar */
.passwordContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.passwordContainer .input {
  padding-right: 50px;
}

.passwordToggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.passwordToggle:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.passwordToggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mensagem de erro */
.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 12px;
  color: #fca5a5;
  font-size: 14px;
  text-align: center;
}

/* Seção de esqueci senha */
.forgotPasswordSection {
  text-align: center;
  margin: 10px 0;
}

.forgotPasswordButton {
  background: none;
  border: none;
  color: #4ade80;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s;
  text-decoration: underline;
}

.forgotPasswordButton:hover:not(:disabled) {
  background: rgba(74, 222, 128, 0.1);
  color: #22c55e;
  text-decoration: none;
}

.forgotPasswordButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Botão de submit */
.submitButton {
  background: linear-gradient(45deg, #4ade80, #22c55e);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 10px;
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 222, 128, 0.3);
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Seção de toggle */
.toggleSection {
  margin-top: 25px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.toggleSection p {
  color: #cccccc;
  font-size: 14px;
  margin: 0 0 10px 0;
}

.toggleButton {
  background: none;
  border: none;
  color: #4ade80;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s;
}

.toggleButton:hover:not(:disabled) {
  background: rgba(74, 222, 128, 0.1);
  color: #22c55e;
}

.toggleButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Informação sobre migração */
.migrationInfo {
  margin-top: 20px;
  background: rgba(74, 222, 128, 0.1);
  border: 1px solid rgba(74, 222, 128, 0.3);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
}

.migrationInfo p {
  color: #4ade80;
  font-size: 13px;
  margin: 0;
  font-weight: 500;
}

/* Responsividade */
@media (max-width: 480px) {
  .overlay {
    padding-top: 60px; /* Menos espaço no mobile */
  }

  .modal {
    padding: 20px;
    margin: 10px;
    max-width: none;
    max-height: calc(100vh - 80px);
  }

  .title {
    font-size: 20px;
  }

  .input {
    font-size: 16px; /* Evita zoom no iOS */
  }
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal {
  animation: fadeIn 0.3s ease-out;
}

/* Scrollbar customizada */
.modal::-webkit-scrollbar {
  width: 6px;
}

.modal::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.modal::-webkit-scrollbar-thumb {
  background: #4ade80;
  border-radius: 3px;
}

.modal::-webkit-scrollbar-thumb:hover {
  background: #22c55e;
}
